<template>
  <!-- PPT演示覆盖层 - 简单的覆盖层方案，不移动3D场景 -->
  <div class="ppt-demonstration-overlay fixed inset-0 z-50">
    <!-- 右侧PPT面板 -->
    <div class="absolute top-0 right-0 w-1/2 h-full bg-white shadow-2xl">
      <SimplePPTPlayer :ppt-path="pptPath" @close="exitDemonstration" @slide-change="handleSlideChange" />
    </div>

    <!-- 左侧3D场景区域的覆盖层 -->
    <div class="absolute top-0 left-0 w-1/2 h-full pointer-events-none">
      <!-- 3D场景信息覆盖层 -->
      <div class="absolute top-4 left-4 bg-black/70 text-white p-3 rounded backdrop-blur-sm pointer-events-auto z-10">
        <div class="flex items-center space-x-2">
          <EyeOutlined class="text-blue-400" />
          <span class="text-sm">3D场景视角</span>
        </div>
        <div class="text-xs text-gray-300 mt-1">
          <div v-if="currentViewBinding"> 绑定视角: {{ currentViewBinding.name }} </div>
          <div v-else> 自由视角 (未绑定) </div>
        </div>
      </div>

      <!-- 退出演示按钮 -->
      <div class="absolute top-4 right-4 pointer-events-auto z-10">
        <a-button type="primary" danger @click="exitDemonstration" class="flex items-center">
          <CloseOutlined />
          退出演示
        </a-button>
      </div>

      <!-- 3D场景控制提示 -->
      <div class="absolute bottom-20 left-4 bg-black/70 text-white p-3 rounded backdrop-blur-sm pointer-events-auto z-10">
        <div class="text-xs text-gray-300">
          <div>• 鼠标拖拽：旋转视角</div>
          <div>• 滚轮：缩放</div>
          <div>• 右键拖拽：平移</div>
        </div>
      </div>
    </div>

    <!-- 分割线 -->
    <div class="absolute top-0 left-1/2 w-1 h-full bg-gray-600 transform -translate-x-1/2 z-10">
      <div
        class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-12 bg-gray-500 rounded flex items-center justify-center"
      >
        <div class="w-1 h-8 bg-gray-300 rounded"></div>
      </div>
    </div>

    <!-- 演示状态栏 - 调整高度避免遮挡PPT控制栏 -->
    <div class="absolute bottom-0 left-0 w-1/2 bg-black/90 text-white p-2 flex items-center justify-between z-10">
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <FileTextOutlined class="text-blue-400" />
          <span class="text-sm font-medium">PPT演示模式</span>
        </div>
        <div class="text-xs text-gray-400"> 左侧：3D场景联动 | 右侧：PPT播放控制 </div>
      </div>

      <div class="flex items-center space-x-4">
        <!-- 同步状态指示器 -->
        <div class="flex items-center space-x-2">
          <div class="w-2 h-2 rounded-full" :class="syncStatus === 'synced' ? 'bg-green-400' : 'bg-yellow-400'"></div>
          <span class="text-xs text-gray-400">
            {{ syncStatus === 'synced' ? '视角已同步' : '视角未绑定' }}
          </span>
        </div>

        <!-- 演示信息 -->
        <div class="text-xs text-gray-400"> 第 {{ currentSlide + 1 }} / {{ totalSlides }} 页 </div>
      </div>
    </div>

    <!-- 快捷键提示 -->
    <div
      v-if="showShortcuts"
      class="absolute top-1/2 left-1/4 transform -translate-x-1/2 -translate-y-1/2 bg-black/90 text-white p-6 rounded-lg backdrop-blur-sm z-20"
    >
      <div class="text-center mb-4">
        <h3 class="text-lg font-medium">快捷键说明</h3>
      </div>
      <div class="space-y-2 text-sm">
        <div class="flex justify-between">
          <span>上一页：</span>
          <span class="text-blue-400">← 或 PageUp</span>
        </div>
        <div class="flex justify-between">
          <span>下一页：</span>
          <span class="text-blue-400">→ 或 PageDown</span>
        </div>
        <div class="flex justify-between">
          <span>自动播放：</span>
          <span class="text-blue-400">Space</span>
        </div>
        <div class="flex justify-between">
          <span>退出演示：</span>
          <span class="text-blue-400">Esc</span>
        </div>
        <div class="flex justify-between">
          <span>显示/隐藏快捷键：</span>
          <span class="text-blue-400">F1</span>
        </div>
      </div>
      <div class="text-center mt-4">
        <a-button size="small" @click="showShortcuts = false"> 关闭 </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted } from 'vue';
  import { CloseOutlined, EyeOutlined, FileTextOutlined } from '@ant-design/icons-vue';
  import { useGlobalThreeStore } from '../../store/globalThreeStore';
  import SimplePPTPlayer from './SimplePPTPlayer.vue';

  // Props
  interface Props {
    pptPath: string;
  }

  const props = defineProps<Props>();

  // Emits
  const emit = defineEmits<{
    exit: [];
  }>();

  // Store
  const globalThreeStore = useGlobalThreeStore();

  // 响应式数据
  const showShortcuts = ref(false);

  // 计算属性
  const currentSlide = computed(() => globalThreeStore.pptDemonstration.currentSlide);
  const totalSlides = computed(() => globalThreeStore.pptDemonstration.totalSlides);
  const currentViewBinding = computed(() => {
    return globalThreeStore.getPPTViewBinding(currentSlide.value);
  });
  const syncStatus = computed(() => {
    return currentViewBinding.value ? 'synced' : 'unsynced';
  });

  // 方法
  const exitDemonstration = () => {
    // 清理演示状态
    globalThreeStore.setPPTDemonstrationActive(false);
    globalThreeStore.setPPTPlaying(false);

    // 触发退出事件
    emit('exit');
  };

  const handleSlideChange = (slideIndex: number) => {
    // PPT页面变化时的处理逻辑已在PPTPlayer中实现
    console.log('Slide changed to:', slideIndex);
  };

  // 键盘事件处理
  const handleKeyDown = (event: KeyboardEvent) => {
    switch (event.key) {
      case 'Escape':
        exitDemonstration();
        break;
      case 'F1':
        event.preventDefault();
        showShortcuts.value = !showShortcuts.value;
        break;
    }
  };

  // 注意：3D场景重新渲染逻辑已通过全局事件在主场景组件中处理
  // 这里不需要重复实现，globalThreeStore.setPPTDemonstrationActive() 会自动触发事件

  // 生命周期
  onMounted(() => {
    // 设置演示状态（这会自动触发ppt-mode-changed事件，主场景组件会处理3D场景重新渲染）
    globalThreeStore.setPPTDemonstrationActive(true);

    // 添加键盘事件监听
    document.addEventListener('keydown', handleKeyDown);

    // 显示快捷键提示（3秒后自动隐藏）
    showShortcuts.value = true;
    setTimeout(() => {
      showShortcuts.value = false;
    }, 3000);

    console.log('PPT演示模式已启动 - 使用覆盖层方案，3D场景将自动调整为左半屏');
  });

  onUnmounted(() => {
    // 移除键盘事件监听
    document.removeEventListener('keydown', handleKeyDown);

    // 清理演示状态（这会自动触发ppt-mode-changed事件，主场景组件会处理3D场景恢复全屏）
    globalThreeStore.setPPTDemonstrationActive(false);

    console.log('PPT演示模式已退出，3D场景将自动恢复全屏');
  });
</script>

<style scoped>
  .ppt-demonstration-overlay {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  /* 确保覆盖层不影响3D场景的鼠标事件 */
  .ppt-demonstration-overlay {
    pointer-events: none;
  }

  /* 只有特定元素可以接收鼠标事件 */
  .ppt-demonstration-overlay .pointer-events-auto {
    pointer-events: auto;
  }

  /* 右侧PPT面板可以接收所有事件 */
  .ppt-demonstration-overlay > div:first-child {
    pointer-events: auto;
  }
</style>

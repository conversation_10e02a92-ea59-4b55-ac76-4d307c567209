import { loadModelFromCache } from '@/views/scene/lib/load/modelCacheUtils';
import { useGlobalThreeStore } from '@/views/scene/store/globalThreeStore';
import { SceneManager } from '../SceneManager';
import { useGlobSetting } from '/@/hooks/setting';
import { buildingData } from '../../../../data/buildingData';
import { ModelCache } from './ModelCache';
import { initializeLoader, validateModelUrl, createDefaultPlaceholderBox, updatePlaceholderAnimation, removePlaceholder } from './LoaderUtils';
import { BuildingManager } from './BuildingManager';
import * as THREE from 'three';
import { LightingManager } from '../LightingManager';
import { RenderingPipeline } from '../RenderingPipeline';
import { GridFloorManager } from '../effects/GridFloorManager';
import { throttle } from 'lodash-es';

const loadedModels = new Map<string, THREE.Group | THREE.Scene>();

export class ModelLoaderManager {
  private static instance: ModelLoaderManager | null = null;
  private scene: THREE.Scene | null = null;
  private modelUrl = '';
  private globalThreeStore = {} as ReturnType<typeof useGlobalThreeStore>;
  private buildingData = {} as typeof buildingData;
  private loader = {} as THREE.Loader;
  private dracoLoader = {} as THREE.Loader;
  private modelCache = new ModelCache();
  private currentModels: Array<THREE.Group | THREE.Scene> = [];
  private interactableObjects: THREE.Mesh[] = [];
  private onModelLoadedCallbacks: Array<() => void> = [];
  private buildingManager = {} as BuildingManager;
  private modelAnimations = new Map<string, THREE.AnimationClip[]>();
  private loadedModels: Map<string, THREE.Group | THREE.Scene> = loadedModels;
  private glowMaterial = {} as THREE.MeshPhongMaterial;
  private rgbGlowMaterial = {} as THREE.ShaderMaterial;
  private cloudIconMaterial = {} as THREE.ShaderMaterial;
  private colorAnimState = {
    hue: 0,
    direction: 1,
    speed: 0.0001,
    flow: 0,
    flowDirection: 1,
    flowSpeed: 0.01,
    lastUpdate: 0,
  };
  private rgbState = { hue: 0, direction: 1 };
  private cloudIconState = { intensity: 0.0, direction: 1, speed: 0.01 };
  private animationFrames = new Set<number>();
  private parkModel: THREE.Scene | null = null;
  private animationLoop: boolean | null = null;
  private _weatherChangeListener: ((event: CustomEvent<{ weatherType: string }>) => void) | null = null;
  private silentLoading: {
    active: boolean;
    totalModels: number;
    loadedModels: number;
    progress: number;
    modelProgress: Map<string, number>;
  } = {
    active: false,
    totalModels: 0,
    loadedModels: 0,
    progress: 0,
    modelProgress: new Map<string, number>(),
  };
  private placeholderBoxes = new Map<string, THREE.Group>();
  private placeholderAnimationEnabled = true;
  private placeholderAnimationId: number | null = null;
  private lastTime = 0;
  private geometryCache: Map<string, { geometry: THREE.BufferGeometry; useCount: number }> | null = null;
  private rgbMaterials: Set<THREE.Material> | null = null;
  private _interactableObjectsBackup: THREE.Mesh[] | null = null;
  private _cachedObjects: THREE.Object3D[] = [];
  private _updateCachedObjectsThrottled = {} as (() => void) & { cancel: () => void };

  constructor() {
    if (ModelLoaderManager.instance) return ModelLoaderManager.instance;

    const globalThreeStore = useGlobalThreeStore();
    const { modelUrl } = useGlobSetting();

    this.scene = SceneManager.getInstance().scene;
    this.modelUrl = modelUrl || '';
    this.globalThreeStore = globalThreeStore;
    this.buildingData = buildingData;

    const { loader, dracoLoader } = initializeLoader(modelUrl || '');
    this.loader = loader;
    this.dracoLoader = dracoLoader;

    this.modelCache = new ModelCache();

    this.currentModels = [];
    this.interactableObjects = [];
    this.onModelLoadedCallbacks = [];

    this.buildingManager = new BuildingManager(this);

    this.modelAnimations = new Map<string, any>();
    this.loadedModels = loadedModels;

    this.glowMaterial = new THREE.MeshPhongMaterial({
      color: 0x00ffff,
      emissive: 0x00ffff,
      transparent: true,
      opacity: 0.8,
      side: THREE.DoubleSide,
      shininess: 100,
    });

    this.rgbGlowMaterial = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        color: { value: new THREE.Color(0xff0000) },
        speed: { value: 0.5 },
        intensity: { value: 1.0 },
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform float time;
        uniform vec3 color;
        uniform float speed;
        uniform float intensity;
        varying vec2 vUv;

        void main() {
          float flow = fract(vUv.x - time * speed);
          float glow = smoothstep(0.0, 0.5, flow) * smoothstep(1.0, 0.5, flow);
          vec3 finalColor = color * (0.5 + intensity * glow);
          gl_FragColor = vec4(finalColor, 0.8);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide,
    });

    this.cloudIconMaterial = new THREE.ShaderMaterial({
      uniforms: {
        color: { value: new THREE.Color(0x00ffff) },
        intensity: { value: 0.5 },
        flow: { value: 0.0 },
      },
      vertexShader: `
        varying vec2 vUv;
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      fragmentShader: `
        uniform vec3 color;
        uniform float intensity;
        uniform float flow;
        varying vec2 vUv;

        void main() {
          vec3 finalColor = color * (0.7 + 0.3 * flow);
          float alpha = 0.9;
          gl_FragColor = vec4(finalColor, alpha);
        }
      `,
      transparent: true,
      side: THREE.DoubleSide,
    });

    this.colorAnimState = {
      hue: 0,
      direction: 1,
      speed: 0.0001,
      flow: 0,
      flowDirection: 1,
      flowSpeed: 0.01,
      lastUpdate: 0,
    };

    this.rgbState = { hue: 0, direction: 1 };
    this.cloudIconState = { intensity: 0.0, direction: 1, speed: 0.01 };
    this.animationFrames = new Set<number>();
    this.parkModel = null;
    this.animationLoop = null;
    this._weatherChangeListener = null;

    this.silentLoading = {
      active: false,
      totalModels: 0,
      loadedModels: 0,
      progress: 0,
      modelProgress: new Map<string, number>(),
    };

    this.placeholderBoxes = new Map<string, THREE.Group>();
    this.placeholderAnimationEnabled = true;
    this.lastTime = performance.now();
    this.geometryCache = null;
    this.rgbMaterials = null;
    this._interactableObjectsBackup = null;

    this._setupWeatherChangeListener();
    this._setupPlaceholderAnimationLoop();

    // 创建节流版本的更新函数
    this._updateCachedObjectsThrottled = throttle(this._updateCachedObjects.bind(this), 100);

    ModelLoaderManager.instance = this;

    // 添加全局调试函数
    (window as any).debugScene = () => this.debugSceneObjects();
    (window as any).refreshLights = () => this._refreshBuildingLights();
  }

  static getInstance(): ModelLoaderManager {
    if (!ModelLoaderManager.instance) {
      ModelLoaderManager.instance = new ModelLoaderManager();
    }
    return ModelLoaderManager.instance;
  }

  addModelLoadedCallback(callback: () => void): void {
    this.onModelLoadedCallbacks.push(callback);
  }

  private _triggerModelLoadedCallbacks(): void {
    this.onModelLoadedCallbacks.forEach((callback) => callback());
  }

  async loadModelWithCache(path: string, type: string, _version = '1.0'): Promise<void> {
    if (this.loadedModels.has(path)) {
      const existingModel = this.loadedModels.get(path);
      if (existingModel) {
        this.scene!.add(existingModel);
        this._processModel(existingModel as THREE.Scene, type);
        this._triggerModelLoadedCallbacks();
        return;
      }
    }

    this._createModelPlaceholder(path, type);

    try {
      for (let progress = 1; progress <= 99; progress += 10) {
        await new Promise((resolve) => setTimeout(resolve, 100));
        this.globalThreeStore.setLoadModelLoading(Math.floor(progress));
      }

      const cachedScene = await loadModelFromCache(path, this.loader);
      if (cachedScene) {
        this._removePlaceholder(path);
        this._processModel(cachedScene, type);
        this.globalThreeStore.setLoadModelLoading(100);
        this.loadedModels.set(path, cachedScene as unknown as THREE.Group);
        this._triggerModelLoadedCallbacks();
        return;
      }
    } catch (error: any) {
      console.error('解析缓存模型出错:', error);
      this.globalThreeStore.addLoadingError({ message: `解析缓存模型出错: ${error.message}`, timestamp: Date.now() });
    }

    await this.loadModelFromNetwork(path, type);
  }

  loadModelFromNetwork(path: string, type: string): Promise<THREE.Scene> {
    return new Promise(async (resolve, reject) => {
      try {
        const validation = await validateModelUrl(path);
        if (!validation.valid) {
          console.error(`模型URL验证失败:`, validation.message);
          this.globalThreeStore.setLoadModelLoading(0);
          this.globalThreeStore.addLoadingError({ message: `模型URL验证失败: ${validation.message}`, timestamp: Date.now() });
          this._showUserFriendlyError(`模型加载失败: ${validation.message}`);
          reject(new Error(`模型URL验证失败: ${validation.message}`));
          return;
        }

        if (!this.placeholderBoxes.has(path)) {
          this._createModelPlaceholder(path, type);
        }

        console.log(`URL验证通过，开始加载模型: ${path}`);

        let attempts = 0;
        const maxAttempts = 3;

        const loadWithRetry = () => {
          attempts++;
          console.log(`尝试加载模型 (${attempts}/${maxAttempts}): ${path}`);

          this.loader.load(
            path,
            (gltf: unknown) => {
              console.log(`模型加载成功: ${path}`);
              this._removePlaceholder(path);
              const model = (gltf as { scene: THREE.Scene }).scene;
              if (
                (gltf as { animations: THREE.AnimationClip[] }).animations &&
                (gltf as { animations: THREE.AnimationClip[] }).animations.length > 0
              ) {
                this.modelAnimations.set(type, (gltf as { animations: THREE.AnimationClip[] }).animations);
              }
              this._processModel(model, type);
              this.globalThreeStore.setLoadModelLoading(100);
              this.loadedModels.set(path, model as unknown as THREE.Group);
              this._triggerModelLoadedCallbacks();
              resolve(model);
            },
            (xhr: ProgressEvent) => {
              const progress = Math.min(99, Math.max(1, Math.floor((xhr.loaded / xhr.total) * 100)));
              this.globalThreeStore.setLoadModelLoading(progress);
            },
            (error: unknown) => {
              console.error(`加载GLTF数据出错 (${attempts}/${maxAttempts}):`, error);
              if (error instanceof Error && error.toString().includes('is not valid JSON')) {
                console.error('收到HTML而非GLTF文件，可能是服务器配置问题或URL错误');
                fetch(path)
                  .then((response) => response.text())
                  .then((text) => {
                    console.error('服务器返回内容的前100个字符:', text.substring(0, 100));
                  })
                  .catch((fetchError) => {
                    console.error('获取响应内容失败:', fetchError);
                  });
              }

              if (attempts < maxAttempts) {
                console.log(`${attempts}/${maxAttempts} 次尝试失败，将在1秒后重试...`);
                setTimeout(loadWithRetry, 1000);
              } else {
                console.error(`已达到最大尝试次数 (${maxAttempts})，放弃加载`);
                this.globalThreeStore.setLoadModelLoading(0);
                this._removePlaceholder(path);
                this._showUserFriendlyError(`模型加载失败，请检查网络连接或联系管理员`);
                reject(error);
              }
            }
          );
        };

        loadWithRetry();
      } catch (error: any) {
        console.error(`加载模型过程中发生异常:`, error);
        this.globalThreeStore.addLoadingError({ message: `加载模型过程中发生异常: ${error.message}`, timestamp: Date.now() });
        this._removePlaceholder(path);
        this.globalThreeStore.setLoadModelLoading(0);
        reject(error);
      }
    });
  }

  private _showUserFriendlyError(message: string): void {
    console.error(`[用户提示] ${message}`);
    if (window.$message) {
      window.$message.error(message);
    }
  }

  getAnimations(type: string): THREE.AnimationClip[] {
    return this.modelAnimations.get(type) || [];
  }

  async removeCurrentModel(): Promise<void> {
    this.animationFrames.forEach((frameId) => {
      cancelAnimationFrame(frameId);
    });
    this.animationFrames.clear();

    this.rgbState = { hue: 0, direction: 1 };
    this.placeholderBoxes.forEach((box, path) => this._removePlaceholder(path));
    this.placeholderBoxes.clear();

    if (this.currentModels.length === 0) {
      console.warn('没有模型需要移除');
      return;
    }

    while (this.currentModels.length > 0) {
      const model = this.currentModels.pop();
      if (model && (model.userData.type !== 'park' || model !== this.parkModel)) {
        this.scene!.remove(model as THREE.Object3D);
        model.traverse((child: THREE.Object3D) => {
          if (child instanceof THREE.Mesh) {
            if (child.geometry) {
              child.geometry.dispose();
            }
            if (Array.isArray(child.material)) {
              child.material.forEach((mat: THREE.Material) => mat.dispose());
            } else if (child.material) {
              (child.material as THREE.Material).dispose();
            }
          }
        });
      }
    }
  }

  preloadModels(modelPaths: string[]): void {
    modelPaths.forEach((path) => {
      if (!this.loadedModels.has(path)) {
        this.loader.load(path, (gltf: unknown) => {
          const model = (gltf as { scene: THREE.Scene }).scene;
          this.loadedModels.set(path, model as unknown as THREE.Group);
        });
      }
    });
  }
  async showBuilding(buildingPath: string): Promise<THREE.Scene> {
    try {
      const result = await this.buildingManager.switchToBuilding(buildingPath);
      const sceneManager = SceneManager.getInstance();
      if (sceneManager) {
        if (sceneManager.renderer) {
          sceneManager.renderer.setClearColor(0x87ceeb, 1);
        }
        if (sceneManager.skyManager) {
          sceneManager.skyManager.show();
        }
        sceneManager.setDayLighting();

        // 应用外部场景光照设置
        const lightingManager = LightingManager.getInstance();
        if (lightingManager) {
          lightingManager.adjustLightingForWeather('clear');
        }

        // 应用平衡的色调映射，避免建筑过度曝光
        const renderingPipeline = RenderingPipeline.getInstance();
        if (renderingPipeline) {
          renderingPipeline.setToneMapping(THREE.ACESFilmicToneMapping, 0.9, true);
        }
      }

      // 刷新建筑灯光效果，确保包含"灯"字的模型能够发光
      this._refreshBuildingLights();
      return result as unknown as THREE.Scene;
    } catch (error) {
      console.error('切换到建筑模式失败:', error);
      throw error;
    }
  }

  async fadeOutCurrentFloor(): Promise<void> {
    const currentFloor = this.getCurrentFloorModel();
    if (!currentFloor) return;

    return new Promise((resolve) => {
      // 保存原始材质属性并设置透明度
      currentFloor.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh && child.material) {
          // 保存原始材质类型信息
          if (!child.userData.originalMaterialType) {
            if (Array.isArray(child.material)) {
              child.userData.originalMaterialType = child.material.map(
                (mat) =>
                  mat.type ||
                  (mat instanceof THREE.MeshStandardMaterial
                    ? 'MeshStandardMaterial'
                    : mat instanceof THREE.MeshPhysicalMaterial
                      ? 'MeshPhysicalMaterial'
                      : mat instanceof THREE.MeshPhongMaterial
                        ? 'MeshPhongMaterial'
                        : 'MeshBasicMaterial')
              );
            } else {
              child.userData.originalMaterialType =
                child.material.type ||
                (child.material instanceof THREE.MeshStandardMaterial
                  ? 'MeshStandardMaterial'
                  : child.material instanceof THREE.MeshPhysicalMaterial
                    ? 'MeshPhysicalMaterial'
                    : child.material instanceof THREE.MeshPhongMaterial
                      ? 'MeshPhongMaterial'
                      : 'MeshBasicMaterial');
            }
          }

          // 设置透明度
          if (Array.isArray(child.material)) {
            child.material.forEach((material: THREE.Material) => {
              material.transparent = true;
              material.opacity = 1;
              material.needsUpdate = true;
            });
          } else {
            child.material.transparent = true;
            child.material.opacity = 1;
            child.material.needsUpdate = true;
          }
        }
      });

      const startTime = Date.now();
      const duration = 500;

      const animate = () => {
        const progress = (Date.now() - startTime) / duration;
        if (progress >= 1) {
          currentFloor.traverse((child: THREE.Object3D) => {
            if (child instanceof THREE.Mesh && child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach((material: THREE.Material) => {
                  material.opacity = 0;
                  material.needsUpdate = true;
                });
              } else {
                child.material.opacity = 0;
                child.material.needsUpdate = true;
              }
            }
          });
          currentFloor.visible = false;
          resolve();
          return;
        }

        currentFloor.traverse((child: THREE.Object3D) => {
          if (child instanceof THREE.Mesh && child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((material: THREE.Material) => {
                material.opacity = 1 - progress;
                material.needsUpdate = true;
              });
            } else {
              child.material.opacity = 1 - progress;
              child.material.needsUpdate = true;
            }
          }
        });

        requestAnimationFrame(animate);
      };

      animate();
    });
  }

  async fadeInFloorModel(floorModel: THREE.Object3D): Promise<void> {
    if (!floorModel) return;

    return new Promise((resolve) => {
      // 确保材质设置正确并保留原始属性
      floorModel.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh && child.material) {
          // 确保使用正确的材质类型
          if (child.userData.originalMaterialType) {
            // 保留原始材质类型
          } else {
            // 如果没有保存原始材质类型，尝试使用MeshStandardMaterial
            if (Array.isArray(child.material)) {
              child.material.forEach((material: THREE.Material) => {
                // 保留材质的所有属性，只设置透明度
                material.transparent = true;
                material.opacity = 0;
                material.needsUpdate = true;
              });
            } else {
              // 保留材质的所有属性，只设置透明度
              child.material.transparent = true;
              child.material.opacity = 0;
              child.material.needsUpdate = true;
            }
          }
        }
      });

      floorModel.visible = true;
      const startTime = Date.now();
      const duration = 500;

      const animate = () => {
        const progress = (Date.now() - startTime) / duration;
        if (progress >= 1) {
          floorModel.traverse((child: THREE.Object3D) => {
            if (child instanceof THREE.Mesh && child.material) {
              if (Array.isArray(child.material)) {
                child.material.forEach((material: THREE.Material) => {
                  material.opacity = 1;
                  material.needsUpdate = true;
                });
              } else {
                child.material.opacity = 1;
                child.material.needsUpdate = true;
              }
            }
          });
          resolve();
          return;
        }

        floorModel.traverse((child: THREE.Object3D) => {
          if (child instanceof THREE.Mesh && child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((material: THREE.Material) => {
                material.opacity = progress;
                material.needsUpdate = true;
              });
            } else {
              child.material.opacity = progress;
              child.material.needsUpdate = true;
            }
          }
        });

        requestAnimationFrame(animate);
      };

      animate();
    });
  }

  async showFloor(floorId: string): Promise<THREE.Object3D> {
    try {
      await this.fadeOutCurrentFloor();
      const floorModel = await this.buildingManager.switchToFloor(floorId);
      if (!floorModel) {
        throw new Error('新楼层模型加载失败');
      }
      const sceneManager = SceneManager.getInstance();
      if (sceneManager) {
        if (sceneManager.renderer) {
          sceneManager.renderer.setClearColor(0x111111, 1); // 使用深灰色背景
        }
        if (sceneManager.skyManager) {
          // 内景模式：完全隐藏天空和云朵
          sceneManager.skyManager.hideSky();
        }
        sceneManager.setNightLighting();
        const lightingManager = LightingManager.getInstance();
        if (lightingManager) {
          // 调整光照但保留之前的设置
          lightingManager.adjustLightingForWeather('dark');
        }
        sceneManager.switchSceneType('interior');
        console.log('[ModelLoaderManager] 楼层显示：已隐藏天空元素');

        // 验证网格地板状态
        setTimeout(() => {
          const gridFloorManager = GridFloorManager.getInstance();
          const gridFloor = gridFloorManager.gridFloor;
          if (gridFloor && sceneManager.scene.children.includes(gridFloor)) {
            console.log('[ModelLoaderManager] 验证：网格地板在场景切换后仍在场景中');
          } else {
            console.warn('[ModelLoaderManager] 警告：网格地板在场景切换后丢失！');
            // 尝试重新显示网格地板
            gridFloorManager.show();
            console.log('[ModelLoaderManager] 已尝试重新显示网格地板');
          }
        }, 200);
      }
      await this.fadeInFloorModel(floorModel);
      return floorModel;
    } catch (error) {
      console.error('楼层切换失败:', error);
      throw error;
    }
  }

  async showExterior(): Promise<void> {
    const sceneManager = SceneManager.getInstance();
    sceneManager.switchSceneType('exterior');

    // 确保应用外部场景光照设置
    const lightingManager = LightingManager.getInstance();
    if (lightingManager) {
      lightingManager.adjustLightingForWeather('clear');
    }

    // 确保应用平衡的色调映射，避免建筑过度曝光
    const renderingPipeline = RenderingPipeline.getInstance();
    if (renderingPipeline) {
      renderingPipeline.setToneMapping(THREE.ACESFilmicToneMapping, 0.9, true);
    }

    // 刷新建筑灯光效果，确保包含"灯"字的模型能够发光
    this._refreshBuildingLights();
  }

  /**
   * 仅加载外景模型而不切换场景类型
   * 用于PPT视角绑定中的外景预览，避免组件关闭
   */
  async loadExteriorModelOnly(): Promise<void> {
    try {
      console.log('[ModelLoaderManager] 仅加载外景模型，不切换场景类型');

      // 获取建筑数据
      const { buildingData } = await import('@/data/buildingData');
      if (!buildingData.modelPath) {
        throw new Error('缺少外景模型路径');
      }

      // 隐藏当前楼层模型（如果有的话）
      await this.fadeOutCurrentFloor();

      // 加载外景模型但不切换场景类型
      await this.loadModelWithCache(buildingData.modelPath, 'exterior');

      // 刷新建筑灯光效果，确保包含"灯"字的模型能够发光
      this._refreshBuildingLights();

      // 触发模型加载完成回调
      this._triggerModelLoadedCallbacks();

      console.log('[ModelLoaderManager] 外景模型加载完成，场景类型保持不变');
    } catch (error) {
      console.error('[ModelLoaderManager] 加载外景模型失败:', error);
      throw error;
    }
  }
  private _processModel(model: THREE.Scene, type: string): void {
    model.userData.type = type;
    model.position.set(0, 0, 0);

    if (type === 'exterior') {
      this.parkModel = model; // 继续使用 parkModel 变量来保持兼容性
      model.position.y -= 2.7;
    }

    if (!this.geometryCache) {
      this.geometryCache = new Map<string, { geometry: THREE.BufferGeometry; useCount: number }>();
    }

    if (type === 'building' || type === 'exterior') {
      console.log(`[ModelLoaderManager] 开始处理${type}类型模型的灯光效果`);
      let lightObjectsFound = 0;
      let allMeshNames: string[] = [];

      model.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh) {
          // 记录所有网格的名称用于调试
          allMeshNames.push(child.name);

          const geoHash = child.geometry.uuid;
          if (this.geometryCache!.has(geoHash)) {
            const cachedGeometry = this.geometryCache!.get(geoHash);
            if (cachedGeometry!.useCount > 3) {
              child.geometry.dispose();
              child.geometry = cachedGeometry!.geometry;
            }
          } else {
            this.geometryCache!.set(geoHash, {
              geometry: child.geometry,
              useCount: 1,
            });
          }

          // 调试：记录所有包含"灯"字的模型名称
          if (child.name.includes('灯')) {
            console.log(`[ModelLoaderManager] 发现包含"灯"字的模型: ${child.name}`);
            lightObjectsFound++;
          }

          if (child.name.includes('云图标')) {
            const material = this.cloudIconMaterial.clone();
            child.material = material;
            child.userData.isCloudIcon = true;
            const color = new THREE.Color();
            color.setHSL(this.colorAnimState.hue, 0.8, 0.5);
            material.uniforms.color.value.copy(color);
            console.log(`[ModelLoaderManager] 应用云图标材质: ${child.name}`);
          } else if (
            child.name.includes('墙面装饰') ||
            child.name.includes('灯条') ||
            child.name.includes('灯') ||
            child.name.includes('light') ||
            child.name.includes('Light') ||
            child.name.includes('LED') ||
            child.name.includes('lamp') ||
            child.name.includes('Lamp')
          ) {
            console.log(`[ModelLoaderManager] 应用发光材质: ${child.name}`);
            if (child.name.includes('RGB')) {
              const material = this.rgbGlowMaterial.clone();
              child.material = material;
              child.userData.isRGBLight = true;
              this._trackRGBMaterial(material);
              const color = new THREE.Color();
              color.setHSL(this.colorAnimState.hue, 0.8, 0.5);
              material.uniforms.color.value.copy(color);
              console.log(`[ModelLoaderManager] 应用RGB发光材质: ${child.name}`);
            } else {
              child.material = this.glowMaterial;
              child.userData.isGlowLight = true;
              console.log(`[ModelLoaderManager] 应用普通发光材质: ${child.name}`);
            }
            child.visible = true;
          }
        }
      });

      console.log(`[ModelLoaderManager] ${type}模型处理完成，共发现${lightObjectsFound}个包含"灯"字的模型`);
      console.log(`[ModelLoaderManager] 模型中所有网格名称 (共${allMeshNames.length}个):`, allMeshNames);

      // 查找可能的灯光相关名称
      const lightRelatedNames = allMeshNames.filter(
        (name) =>
          name.includes('灯') ||
          name.includes('light') ||
          name.includes('Light') ||
          name.includes('LIGHT') ||
          name.includes('lamp') ||
          name.includes('Lamp') ||
          name.includes('LED') ||
          name.includes('led')
      );
      console.log(`[ModelLoaderManager] 发现可能的灯光相关名称:`, lightRelatedNames);

      this._setupAnimationLoop();
    }

    this.interactableObjects = [];
    model.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh) {
        if (type === 'park') {
          child.castShadow = false;
          child.receiveShadow = false;
        }
        if (type === 'floor') {
          this.interactableObjects.push(child);
        }
      }
    });

    this.scene!.add(model);
    this.currentModels.push(model);
    this._cacheSceneObjects();
  }

  getInteractableObjects(): THREE.Mesh[] {
    return this.interactableObjects;
  }

  dispose(): void {
    if (this.animationLoop) {
      this.animationLoop = false;
    }
    if (this.cloudIconMaterial) {
      this.cloudIconMaterial.uniforms.time.value = 0;
    }
    if (this.placeholderAnimationId) {
      cancelAnimationFrame(this.placeholderAnimationId);
      this.placeholderAnimationId = null;
    }
    this.placeholderBoxes.forEach((box, path) => {
      this._removePlaceholder(path);
    });
    this.placeholderBoxes.clear();
    this.placeholderAnimationEnabled = false;
    this.animationFrames.forEach((frameId) => {
      cancelAnimationFrame(frameId);
    });
    this.animationFrames.clear();
    if (this.dracoLoader) {
      // @ts-ignore
      this.dracoLoader.dispose();
      // @ts-ignore
      this.dracoLoader = null;
    }
    this.loadedModels.forEach((model) => {
      model.traverse((child: THREE.Object3D) => {
        if (child instanceof THREE.Mesh) {
          if (child.geometry) {
            child.geometry.dispose();
          }
          if (Array.isArray(child.material)) {
            child.material.forEach((material: THREE.Material) => material.dispose());
          } else if (child.material) {
            (child.material as THREE.Material).dispose();
          }
        }
      });
    });
    this.loadedModels.clear();
    this.removeCurrentModel();
    this.scene = null;
    // @ts-ignore
    this.containerRef = null;
    // @ts-ignore
    this.buildingData = null;
    // @ts-ignore
    this.loader = null;
    this.interactableObjects = [];
    this.modelAnimations.clear();
    if (this._weatherChangeListener) {
      window.removeEventListener('weather-effect-changed', this._weatherChangeListener as EventListener);
    }
    // 取消节流函数
    if (this._updateCachedObjectsThrottled) {
      this._updateCachedObjectsThrottled.cancel();
    }
    ModelLoaderManager.instance = null;
  }

  backupInteractableObjects(): void {
    this._interactableObjectsBackup = [...this.interactableObjects];
  }

  restoreInteractableObjects(): void {
    if (this._interactableObjectsBackup) {
      this.interactableObjects = [...this._interactableObjectsBackup];
    }
  }

  async silentLoadModel(path: string, type: string = 'unknown'): Promise<void> {
    try {
      if (this.loadedModels.has(path)) {
        console.log('模型已在缓存中，跳过加载:', path);
        return;
      }
      console.log('开始静默加载模型:', path);
      const validation = await validateModelUrl(path);
      if (!validation.valid) {
        console.warn(`静默加载 - 模型URL验证失败: ${validation.message}`);
        return;
      }
      const gltf = await new Promise<unknown>((resolve, reject) => {
        this.loader.load(
          path,
          resolve,
          (xhr: ProgressEvent) => {
            if (xhr.lengthComputable) {
              const progress = Math.floor((xhr.loaded / xhr.total) * 100);
              this._updateSilentLoadingProgress(path, progress);
            }
          },
          (error: unknown) => {
            console.warn(`静默加载 - 加载失败:`, error);
            reject(error);
          }
        );
      });
      const model = (gltf as { scene: THREE.Scene }).scene;
      this.loadedModels.set(path, model as unknown as THREE.Group);
      if ((gltf as { animations: THREE.AnimationClip[] }).animations && (gltf as { animations: THREE.AnimationClip[] }).animations.length > 0) {
        this.modelAnimations.set(type, (gltf as { animations: THREE.AnimationClip[] }).animations);
      }
      this._incrementSilentLoadedModels();
      console.log('Silent model load complete:', path);
    } catch (error) {
      console.warn('静默加载模型失败:', path, error);
      this._incrementSilentLoadedModels();
    }
  }

  async batchSilentLoad(modelPaths: string[]): Promise<void> {
    if (!modelPaths || modelPaths.length === 0) return;
    console.log(`开始批量静默加载，模型数量: ${modelPaths.length}，路径:`, modelPaths);
    this.silentLoading = {
      active: true,
      totalModels: modelPaths.length,
      loadedModels: 0,
      progress: 0,
      modelProgress: new Map<string, number>(),
    };
    this._dispatchSilentLoadingEvent();
    const globalThreeStore = this.globalThreeStore;
    console.log(`[ModelLoaderManager] 静默加载前全局计数 - 总数:${globalThreeStore.totalModelsToLoad}, 已加载:${globalThreeStore.loadedModelsCount}`);
    const expectedModels = 2 + (this.buildingData.floors ? this.buildingData.floors.length : 0);
    if (globalThreeStore.totalModelsToLoad <= expectedModels) {
      console.log(`[ModelLoaderManager] 静默加载的模型已包含在计划内，不增加总数计数`);
    } else {
      const currentTotal = globalThreeStore.totalModelsToLoad;
      globalThreeStore.setModelsLoadInfo(currentTotal + modelPaths.length, globalThreeStore.loadedModelsCount);
      console.log(`[ModelLoaderManager] 增加静默加载模型到总数计数: ${currentTotal} -> ${currentTotal + modelPaths.length}`);
    }
    const loadPromises = modelPaths.map((path) => this.silentLoadModel(path));
    const results = await Promise.allSettled(loadPromises);
    const succeeded = results.filter((r) => r.status === 'fulfilled').length;
    console.log(`[ModelLoaderManager] 批量静默加载完成，成功: ${succeeded}/${modelPaths.length}`);
    for (let i = 0; i < succeeded; i++) {
      globalThreeStore.incrementLoadedModels();
    }
    this.silentLoading.progress = 100;
    this._dispatchSilentLoadingEvent();
    await new Promise((resolve) => setTimeout(resolve, 500));
    this.onModelLoadedCallbacks.forEach((callback) => {
      try {
        callback();
      } catch (e) {
        console.error('Error in model loaded callback:', e);
      }
    });
    this.silentLoading.active = false;
    this._dispatchSilentLoadingEvent();
    if (globalThreeStore.loadedModelsCount >= globalThreeStore.totalModelsToLoad) {
      console.log('全部模型（包括静默加载模型）已加载完成，设置应用程序就绪状态');
      globalThreeStore.setLoadingComplete(true);
      globalThreeStore.setAppReady(true);
    } else {
      console.log(
        `模型加载进度：${globalThreeStore.loadedModelsCount}/${globalThreeStore.totalModelsToLoad} (${Math.floor((globalThreeStore.loadedModelsCount / globalThreeStore.totalModelsToLoad) * 100)}%)`
      );
    }
  }

  private _updateSilentLoadingProgress(path: string, singleProgress: number): void {
    if (!this.silentLoading.active) return;
    this.silentLoading.modelProgress!.set(path, singleProgress);
    const totalProgress = Array.from(this.silentLoading.modelProgress!.values()).reduce((a, b) => a + b, 0);
    const avgProgress = Math.floor(totalProgress / this.silentLoading.totalModels);
    this.silentLoading.progress = avgProgress;
    this._dispatchSilentLoadingEvent();
  }

  private _incrementSilentLoadedModels(): void {
    if (!this.silentLoading.active) return;
    this.silentLoading.loadedModels++;
    const overallProgress = Math.floor((this.silentLoading.loadedModels / this.silentLoading.totalModels) * 100);
    this.silentLoading.progress = overallProgress;
    this._dispatchSilentLoadingEvent();
    if (this.silentLoading.loadedModels >= this.silentLoading.totalModels) {
      setTimeout(() => {
        this.silentLoading.active = false;
        this._dispatchSilentLoadingEvent();
      }, 500);
    }
  }

  private _dispatchSilentLoadingEvent(): void {
    const event = new CustomEvent('silent-model-loading', {
      detail: {
        ...this.silentLoading,
      },
    });
    window.dispatchEvent(event);
  }

  findObjectByName(name: string): THREE.Object3D | null {
    if (!this.scene) return null;
    let foundObject: THREE.Object3D | null = null;
    this.scene.traverse((object: THREE.Object3D) => {
      if (object.name === name || object.name.includes(name)) {
        foundObject = object;
      }
    });
    if (!foundObject) {
      console.warn(`Object with name "${name}" not found in scene`);
    }
    return foundObject;
  }

  private _setupAnimationLoop(): void {
    if (this.animationLoop) return;
    const animate = () => {
      const now = Date.now() * 0.001;
      if (now - this.colorAnimState.lastUpdate > 0.02) {
        this.colorAnimState.lastUpdate = now;
        this.colorAnimState.hue += this.colorAnimState.speed * this.colorAnimState.direction;
        if (this.colorAnimState.hue >= 1) {
          this.colorAnimState.direction = -1;
        } else if (this.colorAnimState.hue <= 0) {
          this.colorAnimState.direction = 1;
        }
        this.colorAnimState.flow += this.colorAnimState.flowSpeed * this.colorAnimState.flowDirection;
        if (this.colorAnimState.flow >= 1) {
          this.colorAnimState.flow = 1;
          this.colorAnimState.flowDirection = -1;
        } else if (this.colorAnimState.flow <= 0) {
          this.colorAnimState.flow = 0;
          this.colorAnimState.flowDirection = 1;
        }
        const color = new THREE.Color();
        color.setHSL(this.colorAnimState.hue, 0.8, 0.5);
        if (this.glowMaterial) {
          this.glowMaterial.color.copy(color);
          this.glowMaterial.emissive.copy(color);
          this.glowMaterial.emissiveIntensity = 0.8;
        }
        // 更新RGB材质
        this._updateRGBMaterials(color);
        // 使用节流版本的更新函数
        this._updateCachedObjectsThrottled();
      }
      const frameId = requestAnimationFrame(animate);
      this.animationFrames.add(frameId);
      this.animationFrames.delete(frameId);
    };
    this.animationFrames.add(requestAnimationFrame(animate));
    this.animationLoop = true;
  }

  private _updateRGBMaterials(color: THREE.Color): void {
    if (this.rgbMaterials && this.rgbMaterials.size > 0) {
      this.rgbMaterials.forEach((material: THREE.Material) => {
        (material as THREE.MeshPhongMaterial).emissive = color;
        (material as THREE.MeshPhongMaterial).emissiveIntensity = 0.5;
      });
    }
  }

  private _trackRGBMaterial(material: THREE.Material): void {
    if (!this.rgbMaterials) {
      this.rgbMaterials = new Set<THREE.Material>();
    }
    this.rgbMaterials.add(material);
  }

  private _setupWeatherChangeListener(): void {
    this._weatherChangeListener = (event: CustomEvent<{ weatherType: string }>) => {
      // 不再需要根据天气变化刷新建筑灯光
    };
  }

  private _refreshBuildingLights(): void {
    if (!this.scene) return;
    console.log('[ModelLoaderManager] 开始刷新建筑灯光效果');
    const color = new THREE.Color();
    color.setHSL(this.colorAnimState.hue, 0.8, 0.5);
    let refreshedLights = 0;

    this.scene.traverse((child: THREE.Object3D) => {
      if (
        child instanceof THREE.Mesh &&
        (child.name.includes('灯条') ||
          child.name.includes('墙面装饰') ||
          child.name.includes('灯') ||
          child.name.includes('light') ||
          child.name.includes('Light') ||
          child.name.includes('LED') ||
          child.name.includes('lamp') ||
          child.name.includes('Lamp'))
      ) {
        console.log(
          `[ModelLoaderManager] 刷新灯光: ${child.name}, isRGBLight: ${child.userData.isRGBLight}, isGlowLight: ${child.userData.isGlowLight}`
        );
        refreshedLights++;

        if (child.userData.isRGBLight) {
          const material = this.rgbGlowMaterial.clone();
          material.uniforms.color.value.copy(color);
          child.material = material;
          console.log(`[ModelLoaderManager] 应用RGB材质到: ${child.name}`);
        } else if (child.userData.isGlowLight) {
          child.material = this.glowMaterial;
          console.log(`[ModelLoaderManager] 应用发光材质到: ${child.name}`);
        } else {
          // 如果没有标记，重新应用发光材质
          child.material = this.glowMaterial;
          child.userData.isGlowLight = true;
          console.log(`[ModelLoaderManager] 重新应用发光材质到: ${child.name}`);
        }
        child.visible = true;
      }
      if (child instanceof THREE.Mesh && child.name.includes('云图标')) {
        const material = this.cloudIconMaterial.clone();
        material.uniforms.color.value.copy(color);
        child.material = material;
        child.userData.isCloudIcon = true;
      }
    });

    console.log(`[ModelLoaderManager] 建筑灯光刷新完成，共处理${refreshedLights}个灯光对象`);
    this.animationLoop = null;
    this._setupAnimationLoop();
  }

  private _setupPlaceholderAnimationLoop(): void {
    const animate = () => {
      if (!this.placeholderAnimationEnabled) return;
      const currentTime = performance.now();
      const deltaTime = (currentTime - this.lastTime) / 1000;
      this.lastTime = currentTime;
      this.placeholderBoxes.forEach((box) => {
        updatePlaceholderAnimation(box, deltaTime);
      });
      this.placeholderAnimationId = requestAnimationFrame(animate);
    };
    this.placeholderAnimationId = requestAnimationFrame(animate);
  }

  private _createModelPlaceholder(modelPath: string, type: string): THREE.Group {
    if (this.placeholderBoxes.has(modelPath)) {
      this._removePlaceholder(modelPath);
    }
    this._playHologramSound();
    const placeholder = createDefaultPlaceholderBox(this.scene!, type);
    this.placeholderBoxes.set(modelPath, placeholder);
    if (this.dracoLoader) {
      // @ts-ignore
      this.dracoLoader._placeholderCallback = (min: THREE.Vector3, max: THREE.Vector3, size: THREE.Vector3) => {
        if (placeholder && min && max && size) {
          const center = new THREE.Vector3();
          center.addVectors(min, max).multiplyScalar(0.5);
          placeholder.position.copy(center);
          const mainBox = placeholder.children[0];
          if (mainBox instanceof THREE.Mesh && mainBox.geometry) {
            if (mainBox instanceof THREE.Mesh && mainBox.geometry instanceof THREE.BoxGeometry) {
              const scaleX = Math.max(1, size.x) / mainBox.geometry.parameters.width;
              const scaleY = Math.max(1, size.y) / mainBox.geometry.parameters.height;
              const scaleZ = Math.max(1, size.z) / mainBox.geometry.parameters.depth;
              placeholder.scale.set(scaleX, scaleY, scaleZ);
            }
          }
          this._playHologramUpdateSound();
        }
      };
    }
    placeholder.scale.set(0.1, 0.1, 0.1);
    this._animatePlaceholderEntry(placeholder);
    return placeholder;
  }

  private _animatePlaceholderEntry(placeholder: THREE.Group): void {
    placeholder.children.forEach((child) => {
      if (child instanceof THREE.Mesh && child.material) {
        if (Array.isArray(child.material)) {
          child.material.forEach((mat: THREE.Material) => {
            (mat as THREE.MeshBasicMaterial).opacity = 0;
          });
        } else {
          (child.material as THREE.MeshBasicMaterial).opacity = 0;
        }
      }
    });
    const duration = 0.8;
    const finalScale = 1;
    const startTime = performance.now();
    const animate = () => {
      const now = performance.now();
      const elapsed = (now - startTime) / 1000;
      const progress = Math.min(elapsed / duration, 1);
      const easeOutBack = (t: number) => {
        const c1 = 1.70158;
        const c3 = c1 + 1;
        return 1 + c3 * Math.pow(t - 1, 3) + c1 * Math.pow(t - 1, 2);
      };
      const easeValue = easeOutBack(progress);
      placeholder.scale.set(0.1 + (finalScale - 0.1) * easeValue, 0.1 + (finalScale - 0.1) * easeValue, 0.1 + (finalScale - 0.1) * easeValue);
      placeholder.children.forEach((child) => {
        if (child instanceof THREE.Mesh && child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach((mat: THREE.Material) => {
              (mat as THREE.MeshBasicMaterial).opacity = progress;
            });
          } else {
            (child.material as THREE.MeshBasicMaterial).opacity = progress;
          }
        }
      });
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    requestAnimationFrame(animate);
  }

  private _playHologramSound(): void {
    console.log('全息投影占位盒出现');
  }

  private _playHologramUpdateSound(): void {}

  private _removePlaceholder(modelPath: string): void {
    const placeholder = this.placeholderBoxes.get(modelPath);
    if (placeholder) {
      removePlaceholder(this.scene!, placeholder);
      this.placeholderBoxes.delete(modelPath);
    }
  }

  getCurrentFloorModel(): THREE.Object3D | undefined {
    return this.currentModels.find((model) => model.userData.type === 'floor');
  }

  public getCurrentModels(): (THREE.Scene | THREE.Group)[] {
    return this.currentModels;
  }

  public getScene(): THREE.Scene | null {
    return this.scene;
  }

  public resetAnimations(): void {
    if (this.cloudIconMaterial) {
      this.cloudIconMaterial.uniforms.time.value = 0;
    }
  }

  // 调试方法：手动检查场景中的所有对象
  public debugSceneObjects(): void {
    if (!this.scene) {
      console.log('[ModelLoaderManager] 场景未初始化');
      return;
    }

    console.log('[ModelLoaderManager] 开始调试场景对象...');
    let meshCount = 0;
    let lightObjects: string[] = [];
    let allObjects: string[] = [];

    this.scene.traverse((child: THREE.Object3D) => {
      if (child instanceof THREE.Mesh) {
        meshCount++;
        allObjects.push(child.name);

        if (
          child.name.includes('灯') ||
          child.name.includes('light') ||
          child.name.includes('Light') ||
          child.name.includes('lamp') ||
          child.name.includes('LED')
        ) {
          lightObjects.push(child.name);
          console.log(`[ModelLoaderManager] 灯光对象: ${child.name}, userData:`, child.userData);
        }
      }
    });

    console.log(`[ModelLoaderManager] 场景调试完成:`);
    console.log(`- 总网格数: ${meshCount}`);
    console.log(`- 灯光相关对象: ${lightObjects.length}个`, lightObjects);
    console.log(`- 所有对象名称:`, allObjects);
  }

  private _cacheSceneObjects(): void {
    if (!this.scene) return;
    this._cachedObjects = [];
    this.scene.traverse((object: THREE.Object3D) => {
      if (object instanceof THREE.Mesh) {
        this._cachedObjects.push(object);
      }
    });
  }

  private _updateCachedObjects(): void {
    this._cachedObjects.forEach((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        const color = new THREE.Color();
        color.setHSL(this.colorAnimState.hue, 0.8, 0.5);

        // 更新云图标材质
        if (object.userData.isCloudIcon && (object.material as any).uniforms) {
          (object.material as any).uniforms.color.value.copy(color);
        }

        // 更新RGB发光材质
        if (object.userData.isRGBLight && (object.material as any).uniforms) {
          (object.material as any).uniforms.color.value.copy(color);
        }

        // 更新普通发光材质
        if (object.userData.isGlowLight) {
          (object.material as THREE.MeshPhongMaterial).color.copy(color);
          (object.material as THREE.MeshPhongMaterial).emissive.copy(color);
          (object.material as THREE.MeshPhongMaterial).emissiveIntensity = 0.8;
        }
      }
    });
  }
}
